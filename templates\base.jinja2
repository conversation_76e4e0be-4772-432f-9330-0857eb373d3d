<!DOCTYPE html>
<html lang="en">
  <head>
    <title>
      {% block title %}{% endblock %}
    - Hydro Consulting Applications</title>
    <meta charset="utf-8" />

    <!-- Font Awesome Icons https://fontawesome.com/icons -->
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
          integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer" />

    <!-- Νοτο Variable Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wdth,wght@0,62.5..100,100..900;1,62.5..100,100..900&family=Noto+Serif:ital,wdth,wght@0,62.5..100,100..900;1,62.5..100,100..900&display=swap"
          rel="stylesheet" />

    <!-- Static CSS -->
    <link href="{{ url_for('static', path='/css/styles.css') }}" rel="stylesheet" />

    <!-- favicon -->
    <link id="favicon"
          rel="icon"
          type="image/x-icon"
          href="{{ url_for('static', path='/images/favicon.ico') }}" />

     <!-- DATASTAR -->
    <script type="module" src="https://cdn.jsdelivr.net/gh/starfederation/datastar@v1.0.0-beta.11/bundles/datastar.js"></script>

     <!-- SweetAlert2 2.11 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- css-scope-inline -->
    <!-- <script src="{{ url_for('static', path='/js/css-scope-inline-script.js') }}"></script> -->
    <script src="https://cdn.jsdelivr.net/gh/gnat/css-scope-inline@main/script.js"></script>

    <!-- Random ID Generator -->
    <script>
        function randomId() {
            return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
        }
    </script>

    <!-- Popover Cleanup Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to clean up all popover content
            function cleanupPopovers() {
                const popovers = document.querySelectorAll('.info-content-global');
                popovers.forEach(function(popover) {
                    if (popover.parentNode) {
                        popover.parentNode.removeChild(popover);
                    }
                });
            }

            // Watch for changes to the content-div
            const contentDiv = document.getElementById('content-div');
            if (contentDiv) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        // If the content has been replaced, clean up popovers
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            cleanupPopovers();
                        }
                    });
                });

                observer.observe(contentDiv, { childList: true, subtree: true });
            }
        });
    </script>

    <!-- Safari-Compatible Color Theme Popover Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const popoverButton = document.getElementById('colors-popover-button');
            const popover = document.getElementById('colors-popover');

            if (popoverButton && popover) {
                // Toggle popover visibility
                popoverButton.addEventListener('click', function(event) {
                    event.stopPropagation();

                    if (popover.style.display === 'none' || popover.style.display === '') {
                        // Center the popover on the screen
                        popover.style.top = '50%';
                        popover.style.left = '50%';
                        popover.style.transform = 'translate(-50%, -50%)';
                        popover.style.display = 'block';
                    } else {
                        popover.style.display = 'none';
                    }
                });

                // Close popover when clicking outside
                document.addEventListener('click', function(event) {
                    if (!popover.contains(event.target) && !popoverButton.contains(event.target)) {
                        popover.style.display = 'none';
                    }
                });

                // Prevent popover from closing when clicking inside it
                popover.addEventListener('click', function(event) {
                    event.stopPropagation();
                });
            }
        });
    </script>

  </head>

  <body class="Site" id="body-div">
    <div data-signals="{signalShowContentTransition: true}">
      <style>
          me {
              box-sizing: border-box;
              min-height: 100%;
              display: flex;
              flex-direction: column;
          }
      </style>
      <header>
        <style>
            me {
                background-color: var(--color-background-middle);
            }
        </style>
        <div class="container">
          <section>
            <style>
                me {
                    height: 90px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
            </style>

            <!-- Left: Logo -->
            <div>
              <style>
                  me {
                      display: flex;
                      align-items: center;
                  }
              </style>
              <a href="/" style="cursor: default;">
                <img class="navbarlogo" id="navbar-logo" src="{{ url_for('static', path='/images/hydrapps_orange.svg') }}" />
              </a>
              <script>
                // Set logo immediately based on saved theme to avoid flash
                (function() {
                  const savedTheme = localStorage.getItem('selectedTheme');
                  const logo = document.getElementById('navbar-logo');
                  if (savedTheme && logo) {
                    let logoSrc;
                    if (savedTheme === 'brown') {
                      logoSrc = '{{ url_for("static", path="/images/hydrapps_green.svg") }}';
                    } else if (savedTheme === 'purple') {
                      logoSrc = '{{ url_for("static", path="/images/hydrapps_purple.svg") }}';
                    } else if (savedTheme === 'dark') {
                      logoSrc = '{{ url_for("static", path="/images/hydrapps_dark.svg") }}';
                    } else {
                      logoSrc = '{{ url_for("static", path="/images/hydrapps_orange.svg") }}';
                    }
                    logo.src = logoSrc;
                  }
                })();
              </script>
            </div>

            <!-- Right: Colors button and User menu -->
            <div>
              <style>
                  me {
                      display: flex;
                      column-gap: 20px;
                      align-items: center;
                  }
              </style>

              <button id="colors-popover-button">
                <style>
                    me {
                        background-color: transparent;
                        cursor: pointer;
                        border: 0px;
                        height: 36px;
                        width: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                </style>
                <i class="fa-solid fa-palette">
                  <style>
                      me {
                          font-size: 26px;
                          color: var(--color-text-on-background-middle);
                      }
                  </style>
                </i>
              </button>

              <div id="colors-popover" style="display: none; position: fixed; z-index: 1000;">
                <style>
                    me {
                        width: 400px;
                        background-color: var(--color-background-dark);
                        color: var(--color-text-bright);
                        border: 1px solid var(--color-background-middle);
                        border-width: 1px;
                        border-radius: 16px;
                        padding: 38px 38px;
                        cursor: pointer;
                        text-align: center;
                        font-size: 18px;
                    }
                </style>

                <fieldset>
                  <style>
                      me {
                          display: flex;
                          flex: 1;
                          align-items: center;
                          justify-content: center;
                          column-gap: 10px;
                      }
                  </style>
                  <legend>Color themes:</legend>
                  <div>
                    <input type="radio" id="purple" name="colortheme" value="purple" />
                    <label for="purple">Purple</label>
                  </div>
                  <div>
                    <input type="radio" id="orange" name="colortheme" value="orange" checked />
                    <label for="orange">Orange</label>
                  </div>
                  <div>
                    <input type="radio" id="brown" name="colortheme" value="brown" />
                    <label for="brown">Brown</label>
                  </div>
                  <div>
                    <input type="radio" id="dark" name="colortheme" value="dark" />
                    <label for="dark">Dark</label>
                  </div>
                </fieldset>

                <script>
                  // FOR PURPLE root.style.setProperty('--color-text-brighter', '#fbf2e6');
                  // Add this variable at the top of your existing theme script
                  const useThemeSelector = true; // Set to false to use CSS file colors, true to use theme selector
                  // Modify the existing applyTheme function
                  const originalApplyTheme = applyTheme;
                  applyTheme = function(theme) {
                    // Only apply theme if useThemeSelector is true
                    if (useThemeSelector) {
                      originalApplyTheme(theme);
                    }
                  };
                  // Clear any existing inline styles to ensure CSS file colors are used
                  if (!useThemeSelector) {
                    document.documentElement.removeAttribute('style');
                  }


                  function applyTheme(theme) {
                      const root = document.documentElement;

                      // Update navbar logo based on theme
                      const navbarLogo = document.querySelector('.navbarlogo');
                      if (navbarLogo) {
                          let logoSrc;
                          if (theme === 'brown') {
                              logoSrc = '{{ url_for("static", path="/images/hydrapps_green.svg") }}';
                          } else if (theme === 'purple') {
                              logoSrc = '{{ url_for("static", path="/images/hydrapps_purple.svg") }}';
                          } else if (theme === 'dark') {
                              logoSrc = '{{ url_for("static", path="/images/hydrapps_dark.svg") }}';
                          } else {
                              logoSrc = '{{ url_for("static", path="/images/hydrapps_orange.svg") }}';
                          }
                          navbarLogo.src = logoSrc;
                      }

                      if (theme === 'brown') {
                          root.style.setProperty('--color-background-dark', '#4a3f2c');
                          root.style.setProperty('--color-background-middle', '#b19c77');
                          root.style.setProperty('--color-background-bright', '#e9e3d8');
                          root.style.setProperty('--color-text-on-background-middle', '#464300');
                          root.style.setProperty('--color-text-title', '#000000');
                          root.style.setProperty('--color-text-subtitle', '#000000');
                          root.style.setProperty('--color-text-black', '#000000');
                          root.style.setProperty('--color-text-dark', '#6b4400');
                          root.style.setProperty('--color-text-middle', '#b19c77');
                          root.style.setProperty('--color-text-bright', '#d7d2c9');
                          root.style.setProperty('--color-text-brighter', '#fbf9f6');
                          root.style.setProperty('--color-border-middle', '#c6b18e');
                          root.style.setProperty('--color-input-lines', '#6b4400');
                          root.style.setProperty('--color-hr-lines', '#6b4400');
                          root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                          root.style.setProperty('--color-selected-red', '#ff3300');
                          root.style.setProperty('--color-disabled', '#6f6f6f');
                          root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                          root.style.setProperty('--color-error-title', '#a02300');
                          root.style.setProperty('--padding-border-middle', '4px');
                          root.style.setProperty('--width-border-middle', '4px');
                          root.style.setProperty('--radius-border-middle', '12px');
                      } else if (theme === 'purple') {
                          root.style.setProperty('--color-background-dark', '#663b18');
                          root.style.setProperty('--color-background-middle', '#e0dbcd');
                          root.style.setProperty('--color-background-bright', '#f6f5ee');
                          root.style.setProperty('--color-text-on-background-middle', '#7B3800');
                          root.style.setProperty('--color-text-title', '#000000');
                          root.style.setProperty('--color-text-subtitle', '#000000');
                          root.style.setProperty('--color-text-black', '#000000');
                          root.style.setProperty('--color-text-dark', '#7c3800');
                          root.style.setProperty('--color-text-middle', '#615637');
                          root.style.setProperty('--color-text-bright', '#f6e1c5');
                          root.style.setProperty('--color-text-brighter', '#615637');
                          root.style.setProperty('--color-border-middle', 'rgba(102, 59, 24, 0.2)');
                          root.style.setProperty('--color-input-lines', '#994500');
                          root.style.setProperty('--color-hr-lines', '#6b4400');
                          root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                          root.style.setProperty('--color-selected-red', '#ff3300');
                          root.style.setProperty('--color-disabled', '#6f6f6f');
                          root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                          root.style.setProperty('--color-error-title', '#bc2600');
                          root.style.setProperty('--padding-border-middle', '0px');
                          root.style.setProperty('--width-border-middle', '5px');
                          root.style.setProperty('--radius-border-middle', '10px');
                      } else if (theme === 'dark') {
                          root.style.setProperty('--color-background-dark', '#000000');
                          root.style.setProperty('--color-background-middle', '#464B53');
                          root.style.setProperty('--color-background-bright', '#2d2d2d');
                          root.style.setProperty('--color-text-on-background-middle', '#F0D9C5');
                          root.style.setProperty('--color-text-title', '#FD6630');
                          root.style.setProperty('--color-text-subtitle', '#FEBA9C');
                          root.style.setProperty('--color-text-black', '#F0DAC6');
                          root.style.setProperty('--color-text-dark', '#FEBA9C');
                          root.style.setProperty('--color-text-middle', '#FF6631');
                          root.style.setProperty('--color-text-bright', '#F0DAC6');
                          root.style.setProperty('--color-text-brighter', '#fdf1e6');
                          root.style.setProperty('--color-border-middle', 'rgb(0, 0, 0)');
                          root.style.setProperty('--color-input-lines', '#FEBA9C');
                          root.style.setProperty('--color-hr-lines', '#6b4400');
                          root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                          root.style.setProperty('--color-selected-red', '#ff6161');
                          root.style.setProperty('--color-disabled', '#6f6f6f');
                          root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                          root.style.setProperty('--color-error-title', '#e20000');
                          root.style.setProperty('--padding-border-middle', '0px');
                          root.style.setProperty('--width-border-middle', '5px');
                          root.style.setProperty('--radius-border-middle', '10px');
                      } else {
                          root.style.setProperty('--color-background-dark', '#472c07');
                          root.style.setProperty('--color-background-middle', '#d89c52');
                          root.style.setProperty('--color-background-bright', '#f6e1c5');
                          root.style.setProperty('--color-text-on-background-middle', '#4A2C06');
                          root.style.setProperty('--color-text-title', '#000000');
                          root.style.setProperty('--color-text-subtitle', '#000000');
                          root.style.setProperty('--color-text-black', '#000000');
                          root.style.setProperty('--color-text-dark', '#8b4f06');
                          root.style.setProperty('--color-text-middle', '#945200');
                          root.style.setProperty('--color-text-bright', 'rgb(222, 203, 177)');
                          root.style.setProperty('--color-text-brighter', '#fff6eb');
                          root.style.setProperty('--color-border-middle', '#e5ad63');
                          root.style.setProperty('--color-input-lines', '#d89c52');
                          root.style.setProperty('--color-hr-lines', '#6b4400');
                          root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                          root.style.setProperty('--color-selected-red', '#bc2600');
                          root.style.setProperty('--color-disabled', '#6f6f6f');
                          root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                          root.style.setProperty('--color-error-title', '#ff3700');
                          root.style.setProperty('--padding-border-middle', '4px');
                          root.style.setProperty('--width-border-middle', '4px');
                          root.style.setProperty('--radius-border-middle', '12px');
                      }
                  }

                  // Restore saved theme on page load
                  const savedTheme = localStorage.getItem('selectedTheme');
                  if (savedTheme) {
                      applyTheme(savedTheme);
                      // Check the corresponding radio button
                      const radioButton = document.querySelector(`input[name="colortheme"][value="${savedTheme}"]`);
                      if (radioButton) {
                          radioButton.checked = true;
                      }
                  } else {
                      // Apply default theme (orange) if no saved theme
                      applyTheme('orange');
                  }

                  // Add event listeners to radio buttons
                  document.querySelectorAll('input[name="colortheme"]').forEach(radio => {
                      radio.addEventListener('change', (event) => {
                          const selectedTheme = event.target.value;
                          applyTheme(selectedTheme);
                          // Save theme preference to localStorage
                          localStorage.setItem('selectedTheme', selectedTheme);
                          // Update select arrow images for new theme
                          if (typeof window.updateSelectArrowImage === 'function') {
                              window.updateSelectArrowImage();
                          }
                      });
                  });
                </script>

              </div>
              <div>
                <style>
                    me {
                        margin-right: 10px;
                    }
                </style>
                {% if user is defined and user is not none %}
                <div>
                  <style>
                  me {
                    color: var(--color-text-on-background-middle);
                    position: relative; /* so dropdown is positioned relative to this */
                    display: inline-block;
                  }
                  </style>
                  <input type="checkbox" id="menu">
                  <label for="menu" class="icon">
                      <div class="menu"></div>
                  </label>
                  <div class="dropdown">
                    <div class="dropdown-item" onclick="location.href='/calculations'">
                      <span class="icon"><i class="fa-solid fa-house"></i></span>
                      <span class="text">Home</span>
                    </div>
                    <div class="dropdown-item" onclick="location.href='/userinfo'">
                      <span class="icon"><i class="fa-solid fa-user"></i></span>
                      <span class="text">User Info</span>
                    </div>
                    <div class="dropdown-item" onclick="location.href='/usercustomers'">
                      <span class="icon"><i class="fa-solid fa-users"></i></span>
                      <span class="text">Customers</span>
                    </div>
                    <div class="dropdown-item" onclick="location.href='/logout'">
                      <span class="icon"><i class="fa-solid fa-right-from-bracket"></i></span>
                      <span class="text">Log out</span>
                    </div>
                  </div>
                </div>

                {% else %}
                <button onclick="location.href='/login_form'">
                  <style>
                      me {
                          font-family: 'Noto Sans', sans-serif;
                          font-size: 16px;
                          font-weight: 600;
                          font-stretch: condensed;
                          background-color: transparent;
                          color: var(--color-text-on-background-middle);
                          border: 1px solid var(--color-text-on-background-middle);
                          height: 30px;
                          padding: 0px 16px;
                          cursor: pointer;
                          border-width: 1px;
                          border-radius: 6px;
                          margin-left: auto;
                          transition: background-color 0.3s ease, color 0.3s ease;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                      }

                      me:hover {
                          background-color: var(--color-background-bright);
                          color: var(--color-text-dark);
                      }
                  </style>
                  Log in
                </button>

                {% endif %}
              </div>


            </div>
          </section>
        </div>
      </header>

      <main>
        <style>
            me {
                flex: 1;
            }
        </style>
        <div class="container">
          <div data-class-slidetransition="$signalShowContentTransition" class="content-div slidetransition">
            <div>
              <style>
                  me {
                      display: flex;
                      flex-direction: column;
                      width: auto;
                      max-width: 820px;
                      background-color: var(--color-background-bright);
                      border-radius: 6px;
                      padding-top: 46px;
                      padding-bottom: 16px;
                      padding-left: clamp(18px, 20%, 272px);
                      padding-right: clamp(18px, 20%, 272px);
                  }
              </style>
              <div id="content-div">
                {% block content %}
                {% endblock %}
              </div>
            </div>
          </div>

        </div>
      </main>

      <div>
        <style>
            me {
                width: 100%;
                text-align: center;
                padding-bottom: 20px;
                align-items: center;

            }
        </style>
                {# color: var(--color-text-on-background-middle); #}
        <div class="container">
          <hr class="footer-hr" />
          <div>
            <style>
                me {
                    font-family: 'Noto Serif', serif;
                    font-size: 20px;
                    font-weight: 500;
                    font-stretch: condensed;
                    width: auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                }
            </style>
            <img class="footerlogo" src="{{ url_for('static', path='/images/Hydro_Logomark.png') }}" alt="Logo" />
            <div>Hydro Consulting S.A.</div>
          </div>
          <div>
            <style>
                me {
                    font-family: 'Noto Sans', sans-serif;
                    font-size: 15px;
                    font-weight: 300;
                    font-stretch: normal;
                }
            </style>
            © Copyright 2018-2025. All Rights Reserved.
          </div>
          <div>
            <style>
                me {
                    width: auto;
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    justify-content: center;
                    margin-top: 8px;
                }
            </style>
            <a href=""> <i class="fa-brands fa-square-facebook"></i></a>
            <a href=""> <i class="fa-brands fa-linkedin"></i></a>
            <a href=""> <i class="fa-solid fa-envelope"></i></a>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
